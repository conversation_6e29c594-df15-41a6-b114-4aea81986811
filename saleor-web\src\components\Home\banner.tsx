"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { useEffect, useState, useRef } from "react";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import React from "react";
import { motion } from "framer-motion";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

const spaceBetween = 0;
const slidesPerView = 1;

// 动画变体 - 修正为从上到下的动画
const textVariants = {
	hidden: {
		opacity: 0,
		y: -50, // 从上方开始
		scale: 0.9
	},
	visible: {
		opacity: 1,
		y: 0,
		scale: 1,
		transition: {
			duration: 0.8,
			ease: [0.25, 0.46, 0.45, 0.94]
		}
	}
};

const buttonVariants = {
	hidden: {
		opacity: 0,
		y: -30, // 从上方开始
		scale: 0.8
	},
	visible: {
		opacity: 1,
		y: 0,
		scale: 1,
		transition: {
			duration: 0.6,
			delay: 0.3,
			ease: [0.25, 0.46, 0.45, 0.94]
		}
	},
	hover: {
		scale: 1.05,
		transition: { duration: 0.2 }
	}
};

// 自定义箭头组件 - 优化响应式设计
const CustomArrow = ({ direction, onClick }: { direction: 'prev' | 'next', onClick: () => void }) => {
	return (
		<motion.button
			className={`absolute top-1/2 -translate-y-1/2 z-10
        w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12
        hover:bg-black/80 rounded-full group
        flex items-center justify-center transition-all duration-300
        ${direction === 'prev' ? 'left-2' : 'right-2'}
        hidden sm:flex`}
			onClick={onClick}
		>
			<svg
				width="16"
				height="16"
				viewBox="0 0 24 24"
				fill="none"
				className={`text-black group-hover:text-white  sm:w-5 sm:h-5 lg:w-6 lg:h-6 ${direction === 'next' ? 'rotate-180' : ''}`}
			>
				<path
					d="M15 18L9 12L15 6"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
			</svg>
		</motion.button>
	);
};

const Banner1 = ({ isActive }: { isActive: boolean }) => {
	const t = useTranslations("banner")
	return (
		<div className="relative flex items-center">
			<SEOOptimizedImage
				src="/image/home/<USER>/banner1.png"
				alt="Grope Home Banner 1"
				width={1920}
				height={1080}
				quality={100}
				priority
				className="w-full h-auto object-cover"
			/>
			<div className="absolute inset-0 container flex items-center">
				<div className="">
					<motion.div
						className="max-w-xl sm:max-w-2xl text-[#272829]"
						initial="hidden"
						animate={isActive ? "visible" : "hidden"}
						variants={{
							visible: {
								transition: {
									staggerChildren: 0.2
								}
							}
						}}
					>
						<motion.h1
							className="text-2xl sm:text-4xl md:text-5xl  font-bold mb-4 sm:mb-6 leading-tight"
							variants={textVariants}
						>
							{t("BUILT")}
						</motion.h1>
						<motion.p
							className="text-sm sm:text-base md:text-lg mb-4 sm:mb-8  2xl:mb-20 opacity-90 leading-relaxed max-md:hidden"
							variants={textVariants}
						>
							{t("ms")}
							<br />
							{t("dn")}
						</motion.p>
						<motion.div variants={buttonVariants}>
							<Link
								href={"/products/office-system-furni-ture"}
								className="bg-mainColor hover:bg-opacity-80 text-white hover:text-white
                  px-6 py-3 sm:px-20 sm:py-4 rounded-full uppercase font-semibold
                  text-sm sm:text-lg
                  transition-colors duration-300"
							// variants={buttonVariants}
							>
								{t("Shop now")}
							</Link>
						</motion.div>
					</motion.div>
				</div>
			</div>
		</div>
	);
};

const Banner2 = ({ isActive }: { isActive: boolean }) => {
	const t = useTranslations("banner")
	return (
		<div className="relative flex items-center">
			<SEOOptimizedImage
				src="/image/home/<USER>/banner2.png"
				alt="Grope Home Banner 2"
				width={1920}
				height={1080}
				quality={100}
				priority
				className="w-full h-auto object-cover"
			/>
			<div className="absolute inset-0 container flex items-center">
				<div className="">
					<motion.div
						className="max-w-xl sm:max-w-2xl text-[#272829]"
						initial="hidden"
						animate={isActive ? "visible" : "hidden"}
						variants={{
							visible: {
								transition: {
									staggerChildren: 0.2
								}
							}
						}}
					>
						<motion.h1
							className="text-2xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 !leading-tight"
							variants={textVariants}
						>
							{t("PROFESSIONAL")}
							<br />
							{t("TRAININGEOUIPMENT")}
						</motion.h1>
						<motion.p
							className="text-sm sm:text-base md:text-lg mb-4 sm:mb-8 2xl:mb-20 opacity-90 leading-relaxed max-md:hidden"
							variants={textVariants}
						>
							{t("es")}
							<br />
							{t("te")}
						</motion.p>
						<motion.div variants={buttonVariants}>
							<Link
								href={"/products/training-system"}
								className="bg-mainColor hover:bg-opacity-80 text-white hover:text-white
                  px-6 py-3 sm:px-20 sm:py-4 rounded-full uppercase font-semibold
                  text-sm sm:text-lg
                  transition-colors duration-300"
							// variants={buttonVariants}
							>
								{t("Shop now")}
							</Link>
						</motion.div>
					</motion.div>
				</div>
			</div>
		</div>
	);
};

const Banner3 = ({ isActive }: { isActive: boolean }) => {
	const t = useTranslations("banner")
	return (
		<div className="relative flex items-center">
			<SEOOptimizedImage
				src="/image/home/<USER>/banner3.png"
				alt="Grope Home Banner 3"
				width={1920}
				height={1080}
				quality={100}
				priority
				className="w-full h-auto object-cover"
			/>
			<div className="absolute inset-0 container flex items-center">
				<div className="">
					<motion.div
						className="max-w-xl sm:max-w-2xl text-[#272829]"
						initial="hidden"
						animate={isActive ? "visible" : "hidden"}
						variants={{
							visible: {
								transition: {
									staggerChildren: 0.2
								}
							}
						}}
					>
						<motion.h1
							className="text-2xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 leading-tight"
							variants={textVariants}
						>
							{t("OUIETOFFICE")}
						</motion.h1>
						<motion.p
							className="text-sm sm:text-base md:text-lg mb-4 sm:mb-8 2xl:mb-20 opacity-90 leading-relaxed max-md:hidden"
							variants={textVariants}
						>
							{t("ms")}
							<br />
							{t("dn")}
						</motion.p>
						<motion.div variants={buttonVariants}>
							<Link
								href={"/products/creative-products"}
								className="bg-mainColor hover:bg-opacity-80 text-white hover:text-white
                  px-6 py-3 sm:px-20 sm:py-4 rounded-full uppercase font-semibold
                  text-sm sm:text-lg
                  transition-colors duration-300"
							// variants={buttonVariants}
							>
								{t("Shop now")}
							</Link>
						</motion.div>
					</motion.div>
				</div>
			</div>
		</div>
	);
};

function HomeBanner() {
	const [activeIndex, setActiveIndex] = useState(0);
	const swiperRef = useRef<any>(null);

	const handlePrev = () => {
		if (swiperRef.current) {
			swiperRef.current.slidePrev();
		}
	};

	const handleNext = () => {
		if (swiperRef.current) {
			swiperRef.current.slideNext();
		}
	};

	return (
		<div className="banner relative w-full">
			<Swiper
				ref={swiperRef}
				modules={[Navigation, Autoplay, Pagination]}
				className="home-banner overflow-hidden w-full"
				spaceBetween={spaceBetween}
				slidesPerView={slidesPerView}
				loop={true}
				autoplay={{
					delay: 6000,
					disableOnInteraction: false,
				}}
				onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
				onSwiper={(swiper) => {
					swiperRef.current = swiper;
				}}
			// 添加触摸滑动支持
			// touchRatio={1}
			// touchAngle={45}
			// grabCursor={true}
			>
				<SwiperSlide>
					<Banner1 isActive={activeIndex === 0} />
				</SwiperSlide>
				<SwiperSlide>
					<Banner2 isActive={activeIndex === 1} />
				</SwiperSlide>
				<SwiperSlide>
					<Banner3 isActive={activeIndex === 2} />
				</SwiperSlide>
			</Swiper>

			{/* 自定义箭头 - 仅在桌面端显示 */}
			<CustomArrow direction="prev" onClick={handlePrev} />
			<CustomArrow direction="next" onClick={handleNext} />

			{/* 移动端指示器 */}
			<div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex space-x-2 sm:hidden">
				{[0, 1, 2].map((index) => (
					<button
						key={index}
						className={`w-2 h-2 rounded-full transition-all duration-300 ${activeIndex === index ? 'bg-white' : 'bg-white/50'
							}`}
						onClick={() => swiperRef.current?.slideTo(index)}
					/>
				))}
			</div>
		</div>
	);
}

export default React.memo(HomeBanner);
