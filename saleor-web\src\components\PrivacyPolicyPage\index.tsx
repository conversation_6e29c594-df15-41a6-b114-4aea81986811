'use client'
import React from "react";
import { useTranslations } from "next-intl";
import { HomeTaile } from "@/components/Contact/ConcatPage";

const PrivacyPolicyPage = () => {
    const t = useTranslations()

    return <>
        <HomeTaile msg={t("common.Privacy_Policy")} />
        <section className="py-20">
            <div className="container max-w-4xl mx-auto px-4">
                <div className="prose prose-lg max-w-none">
                    {/* 引言 */}
                    <div className="mb-8">
                        <p className="text-gray-600 leading-relaxed">
                            {t("privacy.introduction")}
                        </p>
                        <p className="text-sm text-gray-500 mt-4">
                            {t("privacy.lastUpdated")}: {t("privacy.updateDate")}
                        </p>
                    </div>

                    {/* 1. 信息收集 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            1. {t("privacy.informationCollectionTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.informationCollectionDesc")}
                        </p>
                        <div className="ml-4">
                            <h3 className="text-lg font-semibold text-gray-700 mb-2">
                                {t("privacy.personalInfoTitle")}
                            </h3>
                            <ul className="list-disc list-inside text-gray-600 space-y-2 mb-4">
                                <li>{t("privacy.personalInfoName")}</li>
                                <li>{t("privacy.personalInfoEmail")}</li>
                                <li>{t("privacy.personalInfoPhone")}</li>
                                <li>{t("privacy.personalInfoAddress")}</li>
                                <li>{t("privacy.personalInfoCompany")}</li>
                            </ul>

                            <h3 className="text-lg font-semibold text-gray-700 mb-2">
                                {t("privacy.technicalInfoTitle")}
                            </h3>
                            <ul className="list-disc list-inside text-gray-600 space-y-2">
                                <li>{t("privacy.technicalInfoIp")}</li>
                                <li>{t("privacy.technicalInfoBrowser")}</li>
                                <li>{t("privacy.technicalInfoDevice")}</li>
                                <li>{t("privacy.technicalInfoCookies")}</li>
                            </ul>
                        </div>
                    </div>

                    {/* 2. 信息使用 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            2. {t("privacy.informationUseTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.informationUseDesc")}
                        </p>
                        <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
                            <li>{t("privacy.informationUseProcessOrders")}</li>
                            <li>{t("privacy.informationUseCustomerService")}</li>
                            <li>{t("privacy.informationUseCommunication")}</li>
                            <li>{t("privacy.informationUseImprovement")}</li>
                            <li>{t("privacy.informationUseMarketing")}</li>
                            <li>{t("privacy.informationUseLegal")}</li>
                        </ul>
                    </div>

                    {/* 3. 信息共享 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            3. {t("privacy.informationSharingTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.informationSharingDesc")}
                        </p>
                        <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
                            <li>{t("privacy.informationSharingServiceProviders")}</li>
                            <li>{t("privacy.informationSharingBusinessTransfers")}</li>
                            <li>{t("privacy.informationSharingLegalRequirements")}</li>
                            <li>{t("privacy.informationSharingConsent")}</li>
                        </ul>
                    </div>

                    {/* 4. 数据安全 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            4. {t("privacy.dataSecurityTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.dataSecurityDesc")}
                        </p>
                        <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
                            <li>{t("privacy.dataSecurityEncryption")}</li>
                            <li>{t("privacy.dataSecurityAccessControl")}</li>
                            <li>{t("privacy.dataSecurityRegularAudits")}</li>
                            <li>{t("privacy.dataSecuritySecureServers")}</li>
                        </ul>
                    </div>

                    {/* 5. Cookies */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            5. {t("privacy.cookiesTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.cookiesDesc")}
                        </p>
                        <div className="ml-4">
                            <h3 className="text-lg font-semibold text-gray-700 mb-2">
                                {t("privacy.cookiesTypesTitle")}
                            </h3>
                            <ul className="list-disc list-inside text-gray-600 space-y-2">
                                <li><strong>{t("privacy.cookiesEssentialName")}:</strong> {t("privacy.cookiesEssentialDesc")}</li>
                                <li><strong>{t("privacy.cookiesPerformanceName")}:</strong> {t("privacy.cookiesPerformanceDesc")}</li>
                                <li><strong>{t("privacy.cookiesFunctionalName")}:</strong> {t("privacy.cookiesFunctionalDesc")}</li>
                                <li><strong>{t("privacy.cookiesMarketingName")}:</strong> {t("privacy.cookiesMarketingDesc")}</li>
                            </ul>
                        </div>
                    </div>

                    {/* 6. 您的权利 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            6. {t("privacy.yourRightsTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.yourRightsDesc")}
                        </p>
                        <ul className="list-disc list-inside text-gray-600 space-y-2 ml-4">
                            <li>{t("privacy.yourRightsAccess")}</li>
                            <li>{t("privacy.yourRightsRectification")}</li>
                            <li>{t("privacy.yourRightsErasure")}</li>
                            <li>{t("privacy.yourRightsRestriction")}</li>
                            <li>{t("privacy.yourRightsPortability")}</li>
                            <li>{t("privacy.yourRightsObjection")}</li>
                        </ul>
                    </div>

                    {/* 7. 数据保留 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            7. {t("privacy.dataRetentionTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed">
                            {t("privacy.dataRetentionDesc")}
                        </p>
                    </div>

                    {/* 8. 第三方链接 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            8. {t("privacy.thirdPartyLinksTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed">
                            {t("privacy.thirdPartyLinksDesc")}
                        </p>
                    </div>

                    {/* 9. 儿童隐私 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            9. {t("privacy.childrenPrivacyTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed">
                            {t("privacy.childrenPrivacyDesc")}
                        </p>
                    </div>

                    {/* 10. 政策更新 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            10. {t("privacy.policyUpdatesTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed">
                            {t("privacy.policyUpdatesDesc")}
                        </p>
                    </div>

                    {/* 11. 联系我们 */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            11. {t("privacy.contactUsTitle")}
                        </h2>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {t("privacy.contactUsDesc")}
                        </p>
                        <div className="bg-gray-50 p-6 rounded-lg">
                            <p className="text-gray-700 mb-2">
                                <strong>{t("common.Company Name")}:</strong> {t("common.company_name")}
                            </p>
                            <p className="text-gray-700 mb-2">
                                <strong>{t("common.Email")}:</strong> <EMAIL>
                            </p>
                            <p className="text-gray-700 mb-2">
                                <strong>{t("common.Address")}:</strong> {t("privacy.contactUsAddress")}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </>
}

export default PrivacyPolicyPage
