export const menus: Menu[] = [
	{ id: 1, name: "menu.home", link: "/", hasSlug: true, show: true },
	{ id: 20, name: "menu.product", link: "/products", hasSlug: true, show: true },
	{ id: 4, name: "menu.aboutUs", link: "/about-us", hasSlug: true, show: true },
	{ id: 3, name: "menu.blog", link: "/blog", hasSlug: true, show: true },
	{ id: 5, name: "menu.contactUs", link: "/contact-us", hasSlug: true, show: true },
	{ id: 6, name: "menu.faqs", link: "/faqs", hasSlug: true, show: false },
	{ id: 111, name: "menu.register", link: "/register", hasSlug: true, show: false },
	{ id: 7, name: "menu.category", link: "/category", hasSlug: true, show: false },
	{ id: 8, name: "menu.tag", link: "/tag", hasSlug: true, show: false },
	{ id: 9, name: "menu.inquiry", link: "/inquiry", hasSlug: true, show: false },
	{ id: 10, name: "menu.checkout", link: "/checkout", hasSlug: true, show: false },
	{ id: 11, name: "menu.collection", link: "/collection", hasSlug: true, show: false },
	{ id: 12, name: "menu.compare", link: "/compare", hasSlug: true, show: false },
	{ id: 51, name: "menu.cases", link: "/cases", hasSlug: true, show: false },
	{ id: 65, name: "menu.faqs", link: "/faqs", hasSlug: true, show: false },
	{ id: 61, name: "menu.PatentCertification", link: "/patent-certification", hasSlug: true, show: false },
	{ id: 13, name: "menu.Tourist0rderInquiry", link: "/tourist-order-inquiry", hasSlug: true, show: false },
];

export interface MenuItem {
	id: number;
	name: string;
	link: string;
	hasSlug: boolean;
	show: boolean;
}

export interface Menu {
	id: number;
	name: string;
	link: string;
	hasSlug: boolean;
	show: boolean;
	children?: MenuItem[];
}
