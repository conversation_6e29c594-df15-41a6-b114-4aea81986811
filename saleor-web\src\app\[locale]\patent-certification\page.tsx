import React from "react";
import { MyPageProps } from "@/lib/@types/base";
import { generateSeo } from "@/lib/utils/seo";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { unstable_setRequestLocale } from "next-intl/server";
import PatentCertificationPage from "@/components/PatentCertificationPage";
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
    props.params.page = ["patent-certification"]
    // 获取基础页面的SEO数据
    const seo = await getBasePageSeo(props);
    // 生成最终的SEO信息，并返回
    return generateSeo(props, {
        ...seo,
        ogType: "website",
    });
};

export default function Page({ params }: MyPageProps) {
    unstable_setRequestLocale(params.locale);
    return <>
        <PatentCertificationPage />
    </>;
}
