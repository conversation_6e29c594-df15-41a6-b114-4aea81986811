"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/layout",{

/***/ "(app-pages-browser)/./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/navigation */ \"(app-pages-browser)/./src/navigation.ts\");\n/* harmony import */ var _components_Image_SEOOptimizedImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Image/SEOOptimizedImage */ \"(app-pages-browser)/./src/components/Image/SEOOptimizedImage.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! swiper/css */ \"(app-pages-browser)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/pagination */ \"(app-pages-browser)/./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! swiper/css/navigation */ \"(app-pages-browser)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_swiper_bundle_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! swiper/swiper-bundle.css */ \"(app-pages-browser)/./node_modules/swiper/swiper-bundle.css\");\n/* harmony import */ var _components_Product_product_compare__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Product/product-compare */ \"(app-pages-browser)/./src/components/Product/product-compare.tsx\");\n/* harmony import */ var _lib_api_product__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api/product */ \"(app-pages-browser)/./src/lib/api/product.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction InspirationCubeLayout(param) {\n    let { children, channel, locale, categoriesData, products } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations)();\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cubeProducts, setCubeProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // 产品图片映射 - 根据产品slug映射到对应的展示图片\n    const productImageMap = {\n        \"cube-s1\": \"/image/ldc/cube S1.png\",\n        \"cube-s2\": \"/image/ldc/cube S2.png\",\n        \"cube-m1-the-ultimate-soundproof-workspace-pod\": \"/image/ldc/cube M1.png\",\n        \"cube-m2-elevate-your-workspace-with-modern-design\": \"/image/ldc/cube M2.png\",\n        \"cube-l1\": \"/image/ldc/cube L1.png\",\n        \"cube-l2\": \"/image/ldc/cube L2.png\"\n    };\n    // 获取产品展示图片\n    const getProductDisplayImage = (product)=>{\n        // 使用映射的图片\n        return productImageMap[product.slug];\n    };\n    // 获取inspiration-cube分类的产品数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchCubeProducts = async ()=>{\n            try {\n                var _response_category_products, _response_category;\n                setLoading(true);\n                const response = await (0,_lib_api_product__WEBPACK_IMPORTED_MODULE_12__.fetchProductByCategoryDataWithLimit)({\n                    slug: \"inspiration-cube\",\n                    locale: locale,\n                    channel: channel,\n                    first: 50,\n                    after: \"\"\n                });\n                if (response === null || response === void 0 ? void 0 : (_response_category = response.category) === null || _response_category === void 0 ? void 0 : (_response_category_products = _response_category.products) === null || _response_category_products === void 0 ? void 0 : _response_category_products.edges) {\n                    const productList = response.category.products.edges.map((edge)=>edge.node);\n                    setCubeProducts(productList);\n                }\n            } catch (error) {\n                console.error(\"Error fetching cube products:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCubeProducts();\n    }, [\n        locale,\n        channel\n    ]);\n    // 滚动控制函数 - 简化版本\n    const scrollToCard = (direction)=>{\n        const container = document.getElementById(\"cardContainer\");\n        if (!container) return;\n        const cardWidth = window.innerWidth >= 768 ? 384 + 24 : 320 + 16; // w-96 + gap-6 或 w-80 + gap-4\n        const scrollAmount = cardWidth; // 每次滚动一个卡片的宽度\n        if (direction === \"next\") {\n            container.scrollBy({\n                left: scrollAmount,\n                behavior: \"smooth\"\n            });\n        } else {\n            container.scrollBy({\n                left: -scrollAmount,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 更新按钮状态\n    const updateButtonStates = (container)=>{\n        const prevBtn = document.getElementById(\"prevBtn\");\n        const nextBtn = document.getElementById(\"nextBtn\");\n        if (prevBtn && nextBtn) {\n            // 禁用/启用左侧按钮\n            if (container.scrollLeft <= 10) {\n                prevBtn.setAttribute(\"disabled\", \"true\");\n            } else {\n                prevBtn.removeAttribute(\"disabled\");\n            }\n            // 禁用/启用右侧按钮\n            const maxScroll = container.scrollWidth - container.clientWidth;\n            if (container.scrollLeft >= maxScroll - 10) {\n                nextBtn.setAttribute(\"disabled\", \"true\");\n            } else {\n                nextBtn.removeAttribute(\"disabled\");\n            }\n        }\n    };\n    // 初始化按钮状态\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const container = document.getElementById(\"cardContainer\");\n        if (container) {\n            // 更新按钮状态\n            updateButtonStates(container);\n        }\n    }, []);\n    // 卡片数据 - 简化版本\n    const cardData = [\n        {\n            id: \"card-1\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-2\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-3\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-4\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-5\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-6\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-7\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-8\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-afe428e068c5954d\" + \" \" + \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"bg-[#fafafc] py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-afe428e068c5954d\" + \" \" + \"container !max-w-[850px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                // initial={{ opacity: 0, y: 30 }}\n                                // animate={{ opacity: 1, y: 0 }}\n                                // transition={{ duration: 0.8, delay: 0.4 }}\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Autoplay,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Pagination\n                                        ],\n                                        spaceBetween: 16,\n                                        slidesPerView: 2,\n                                        // autoplay={{\n                                        //   delay: 3000,\n                                        //   disableOnInteraction: false,\n                                        // }}\n                                        loop: true,\n                                        centeredSlides: false,\n                                        // pagination={{\n                                        //   clickable: true,\n                                        //   bulletClass: 'swiper-pagination-bullet !bg-gray-400',\n                                        //   bulletActiveClass: 'swiper-pagination-bullet-active !bg-gray-800',\n                                        // }}\n                                        navigation: {\n                                            nextEl: \".swiper-button-next-custom\",\n                                            prevEl: \".swiper-button-prev-custom\"\n                                        },\n                                        breakpoints: {\n                                            320: {\n                                                slidesPerView: 3,\n                                                spaceBetween: 12\n                                            },\n                                            640: {\n                                                slidesPerView: 3,\n                                                spaceBetween: 16\n                                            },\n                                            768: {\n                                                slidesPerView: 4,\n                                                spaceBetween: 20\n                                            },\n                                            1024: {\n                                                slidesPerView: 6,\n                                                spaceBetween: 24\n                                            }\n                                        },\n                                        className: \"cube-products-swiper\",\n                                        children: loading ? // 加载状态\n                                        Array.from({\n                                            length: 6\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                className: \"h-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"flex flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-[60px] h-[60px] bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-16 h-3 bg-gray-200 rounded mt-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, \"loading-\".concat(index), false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)) : cubeProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                className: \"h-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.6 + index * 0.1\n                                                    },\n                                                    className: \"group cursor-pointer h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navigation__WEBPACK_IMPORTED_MODULE_3__.Link, {\n                                                        href: \"/product/\".concat(product.slug),\n                                                        className: \"group\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"w-[60px] h-[60px] overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Image_SEOOptimizedImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        src: getProductDisplayImage(product),\n                                                                        alt: product.name,\n                                                                        width: 1000,\n                                                                        height: 100,\n                                                                        className: \"w-full h-full object-contain  transition-transform duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"flex-1 flex flex-col justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[12px] text-[#323232] group-hover:text-[#000] text-center mt-2 line-clamp-1\",\n                                                                        children: [\n                                                                            \"ins.\",\n                                                                            product.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200  flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-black\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8  rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200 flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-black\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[80px] max-lg:py-[40px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex items-center justify-between flex-wrap gap-4 gap-y-8 container mb-[80px] max-lg:mb-[40px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[80px] max-lg:text-[48px] font-semibold\",\n                                        children: \"ins.Cube\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[28px] max-lg:text-[20px] font-semibold\",\n                                        children: \"Only sound for inspiration\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"bg-gray-500 h-[700px]\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-full bg-white py-16 md:py-24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"max-w-[1680px] mx-auto px-4 mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n                                    children: \"来了解一下 Inspiration Cube\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"relative max-2xl:px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"cardContainer\",\n                                    onScroll: (e)=>updateButtonStates(e.target),\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"overflow-x-auto overflow-y-visible scrollbar-hide\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            paddingLeft: \"max(0px, calc((100vw - 1680px) / 2))\",\n                                            paddingRight: \"max(0px, calc((100vw - 1680px) / 2))\"\n                                        },\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"flex gap-4 md:gap-6 pb-4 py-4\",\n                                        children: cardData.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    height: \"calc((100vw - 6rem) * 1.46)\",\n                                                    maxHeight: \"591px\"\n                                                },\n                                                onClick: ()=>setSelectedCard(card),\n                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex-shrink-0 bg-black rounded-3xl relative overflow-hidden hover:scale-[1.02] cursor-pointer transition-transform duration-300 w-[calc(100vw-6rem)] md:w-[405px] \".concat(index === cardData.length - 1 ? \"mr-4 md:mr-6\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"p-6 bg-black text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"text-sm font-medium text-gray-400 mb-2\",\n                                                                children: card.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"text-xl font-bold leading-tight\",\n                                                                children: card.subtitle.split(\"\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-afe428e068c5954d\",\n                                                                        children: [\n                                                                            line,\n                                                                            i < card.subtitle.split(\"\\n\").length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                                className: \"jsx-afe428e068c5954d\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 74\n                                                                            }, this)\n                                                                        ]\n                                                                    }, i, true, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"absolute bottom-4 right-4 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 4v16m8-8H4\",\n                                                                className: \"jsx-afe428e068c5954d\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, card.id, true, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex justify-center items-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        id: \"prevBtn\",\n                                        onClick: ()=>scrollToCard(\"prev\"),\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        id: \"nextBtn\",\n                                        onClick: ()=>scrollToCard(\"next\"),\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Product_product_compare__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            categorySlug: \"inspiration-cube\",\n                            locale: locale,\n                            channel: channel\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: selectedCard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4\",\n                    onClick: ()=>setSelectedCard(null),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-white relative rounded-2xl max-w-[1200px] w-full max-h-[90vh] overflow-y-auto p-[74px] my-8\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setSelectedCard(null);\n                                },\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"absolute top-4 right-4 w-10 h-10 bg-[#333336] backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-opacity-80 transition-colors z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-[#d6d6d7]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\",\n                                        className: \"jsx-afe428e068c5954d\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"relative overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[18px] font-medium text-[#1d1d1f] mb-8\",\n                                            children: selectedCard.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"text-3xl md:text-[50px] font-semibold mb-[64px] text-[#1d1d1f]\",\n                                            children: \"一直飙一直快\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[64px] bg-[#f5f5f7] h-[700px] mb-[50px] rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[64px] bg-[#f5f5f7] h-[700px] rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"afe428e068c5954d\",\n                children: \".cube-products-swiper .swiper-pagination{bottom:0!important}.cube-products-swiper .swiper-pagination-bullet{width:8px!important;height:8px!important;margin:0 4px!important;opacity:.5!important}.cube-products-swiper .swiper-pagination-bullet-active{opacity:1!important}@media(max-width:768px){#cardContainer{-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;-webkit-overflow-scrolling:touch}#cardContainer>div>div{scroll-snap-align:start}}.cube-products-swiper .swiper-slide{height:auto!important}}\\n        .scrollbar-hide {-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide::-webkit-scrollbar{display:none}.overflow-x-auto{scroll-behavior:smooth}@media(max-width:768px){.cube-products-swiper .swiper-pagination{bottom:-8px!important}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InspirationCubeLayout, \"mj8vv/8ODgZaZiUdzoqSil2Ey0o=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations\n    ];\n});\n_c = InspirationCubeLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (/*#__PURE__*/_c1 = react__WEBPACK_IMPORTED_MODULE_2___default().memo(InspirationCubeLayout));\nvar _c, _c1;\n$RefreshReg$(_c, \"InspirationCubeLayout\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx\n"));

/***/ })

});