"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/layout",{

/***/ "(app-pages-browser)/./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/navigation */ \"(app-pages-browser)/./src/navigation.ts\");\n/* harmony import */ var _components_Image_SEOOptimizedImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Image/SEOOptimizedImage */ \"(app-pages-browser)/./src/components/Image/SEOOptimizedImage.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! swiper/css */ \"(app-pages-browser)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/pagination */ \"(app-pages-browser)/./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! swiper/css/navigation */ \"(app-pages-browser)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_swiper_bundle_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! swiper/swiper-bundle.css */ \"(app-pages-browser)/./node_modules/swiper/swiper-bundle.css\");\n/* harmony import */ var _components_Product_product_compare__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Product/product-compare */ \"(app-pages-browser)/./src/components/Product/product-compare.tsx\");\n/* harmony import */ var _lib_api_product__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api/product */ \"(app-pages-browser)/./src/lib/api/product.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction InspirationCubeLayout(param) {\n    let { children, channel, locale, categoriesData, products } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations)();\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cubeProducts, setCubeProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // 产品图片映射 - 根据产品slug映射到对应的展示图片\n    const productImageMap = {\n        \"cube-s1\": \"/image/ldc/cube S1.png\",\n        \"cube-s2\": \"/image/ldc/cube S2.png\",\n        \"cube-m1-the-ultimate-soundproof-workspace-pod\": \"/image/ldc/cube M1.png\",\n        \"cube-m2-elevate-your-workspace-with-modern-design\": \"/image/ldc/cube M2.png\",\n        \"cube-l1\": \"/image/ldc/cube L1.png\",\n        \"cube-l2\": \"/image/ldc/cube L2.png\"\n    };\n    // 获取产品展示图片\n    const getProductDisplayImage = (product)=>{\n        // 使用映射的图片\n        return productImageMap[product.slug];\n    };\n    // 获取inspiration-cube分类的产品数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchCubeProducts = async ()=>{\n            try {\n                var _response_category_products, _response_category;\n                setLoading(true);\n                const response = await (0,_lib_api_product__WEBPACK_IMPORTED_MODULE_12__.fetchProductByCategoryDataWithLimit)({\n                    slug: \"inspiration-cube\",\n                    locale: locale,\n                    channel: channel,\n                    first: 50,\n                    after: \"\"\n                });\n                if (response === null || response === void 0 ? void 0 : (_response_category = response.category) === null || _response_category === void 0 ? void 0 : (_response_category_products = _response_category.products) === null || _response_category_products === void 0 ? void 0 : _response_category_products.edges) {\n                    const productList = response.category.products.edges.map((edge)=>edge.node);\n                    setCubeProducts(productList);\n                }\n            } catch (error) {\n                console.error(\"Error fetching cube products:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCubeProducts();\n    }, [\n        locale,\n        channel\n    ]);\n    // 滚动控制函数 - 简化版本\n    const scrollToCard = (direction)=>{\n        const container = document.getElementById(\"cardContainer\");\n        if (!container) return;\n        const cardWidth = window.innerWidth >= 768 ? 384 + 24 : 320 + 16; // w-96 + gap-6 或 w-80 + gap-4\n        const scrollAmount = cardWidth; // 每次滚动一个卡片的宽度\n        if (direction === \"next\") {\n            container.scrollBy({\n                left: scrollAmount,\n                behavior: \"smooth\"\n            });\n        } else {\n            container.scrollBy({\n                left: -scrollAmount,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 更新按钮状态\n    const updateButtonStates = (container)=>{\n        const prevBtn = document.getElementById(\"prevBtn\");\n        const nextBtn = document.getElementById(\"nextBtn\");\n        if (prevBtn && nextBtn) {\n            // 禁用/启用左侧按钮\n            if (container.scrollLeft <= 10) {\n                prevBtn.setAttribute(\"disabled\", \"true\");\n            } else {\n                prevBtn.removeAttribute(\"disabled\");\n            }\n            // 禁用/启用右侧按钮\n            const maxScroll = container.scrollWidth - container.clientWidth;\n            if (container.scrollLeft >= maxScroll - 10) {\n                nextBtn.setAttribute(\"disabled\", \"true\");\n            } else {\n                nextBtn.removeAttribute(\"disabled\");\n            }\n        }\n    };\n    // 初始化按钮状态\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const container = document.getElementById(\"cardContainer\");\n        if (container) {\n            // 更新按钮状态\n            updateButtonStates(container);\n        }\n    }, []);\n    // 卡片数据 - 简化版本\n    const cardData = [\n        {\n            id: \"card-1\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-2\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-3\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-4\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-5\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-6\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-7\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-8\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-afe428e068c5954d\" + \" \" + \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"bg-[#fafafc] py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-afe428e068c5954d\" + \" \" + \"container !max-w-[850px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                // initial={{ opacity: 0, y: 30 }}\n                                // animate={{ opacity: 1, y: 0 }}\n                                // transition={{ duration: 0.8, delay: 0.4 }}\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Autoplay,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Pagination\n                                        ],\n                                        spaceBetween: 16,\n                                        slidesPerView: 2,\n                                        // autoplay={{\n                                        //   delay: 3000,\n                                        //   disableOnInteraction: false,\n                                        // }}\n                                        loop: true,\n                                        centeredSlides: false,\n                                        // pagination={{\n                                        //   clickable: true,\n                                        //   bulletClass: 'swiper-pagination-bullet !bg-gray-400',\n                                        //   bulletActiveClass: 'swiper-pagination-bullet-active !bg-gray-800',\n                                        // }}\n                                        navigation: {\n                                            nextEl: \".swiper-button-next-custom\",\n                                            prevEl: \".swiper-button-prev-custom\"\n                                        },\n                                        breakpoints: {\n                                            320: {\n                                                slidesPerView: 3,\n                                                spaceBetween: 12\n                                            },\n                                            640: {\n                                                slidesPerView: 3,\n                                                spaceBetween: 16\n                                            },\n                                            768: {\n                                                slidesPerView: 4,\n                                                spaceBetween: 20\n                                            },\n                                            1024: {\n                                                slidesPerView: 6,\n                                                spaceBetween: 24\n                                            }\n                                        },\n                                        className: \"cube-products-swiper\",\n                                        children: loading ? // 加载状态\n                                        Array.from({\n                                            length: 6\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                className: \"h-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"flex flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-[60px] h-[60px] bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-16 h-3 bg-gray-200 rounded mt-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, \"loading-\".concat(index), false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)) : cubeProducts.map((product, index)=>{\n                                            var _product_translation;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                className: \"h-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.6 + index * 0.1\n                                                    },\n                                                    className: \"group cursor-pointer h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navigation__WEBPACK_IMPORTED_MODULE_3__.Link, {\n                                                        href: \"/product/\".concat(product.slug),\n                                                        className: \"group\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"w-[60px] h-[60px] overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Image_SEOOptimizedImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        src: getProductDisplayImage(product),\n                                                                        alt: ((_product_translation = product.translation) === null || _product_translation === void 0 ? void 0 : _product_translation.name) || product.name,\n                                                                        width: 1000,\n                                                                        height: 100,\n                                                                        className: \"w-full h-full object-contain  transition-transform duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"flex-1 flex flex-col justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[12px] text-[#323232] group-hover:text-[#000] text-center mt-2 line-clamp-1\",\n                                                                        children: [\n                                                                            \"ins.\",\n                                                                            xproduct.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200  flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-black\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8  rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200 flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-black\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[80px] max-lg:py-[40px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex items-center justify-between flex-wrap gap-4 gap-y-8 container mb-[80px] max-lg:mb-[40px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[80px] max-lg:text-[48px] font-semibold\",\n                                        children: \"ins.Cube\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[28px] max-lg:text-[20px] font-semibold\",\n                                        children: \"Only sound for inspiration\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"bg-gray-500 h-[700px]\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-full bg-white py-16 md:py-24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"max-w-[1680px] mx-auto px-4 mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n                                    children: \"来了解一下 Inspiration Cube\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"relative max-2xl:px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"cardContainer\",\n                                    onScroll: (e)=>updateButtonStates(e.target),\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"overflow-x-auto overflow-y-visible scrollbar-hide\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            paddingLeft: \"max(0px, calc((100vw - 1680px) / 2))\",\n                                            paddingRight: \"max(0px, calc((100vw - 1680px) / 2))\"\n                                        },\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"flex gap-4 md:gap-6 pb-4 py-4\",\n                                        children: cardData.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    height: \"calc((100vw - 6rem) * 1.46)\",\n                                                    maxHeight: \"591px\"\n                                                },\n                                                onClick: ()=>setSelectedCard(card),\n                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex-shrink-0 bg-black rounded-3xl relative overflow-hidden hover:scale-[1.02] cursor-pointer transition-transform duration-300 w-[calc(100vw-6rem)] md:w-[405px] \".concat(index === cardData.length - 1 ? \"mr-4 md:mr-6\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"p-6 bg-black text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"text-sm font-medium text-gray-400 mb-2\",\n                                                                children: card.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"text-xl font-bold leading-tight\",\n                                                                children: card.subtitle.split(\"\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-afe428e068c5954d\",\n                                                                        children: [\n                                                                            line,\n                                                                            i < card.subtitle.split(\"\\n\").length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                                className: \"jsx-afe428e068c5954d\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 74\n                                                                            }, this)\n                                                                        ]\n                                                                    }, i, true, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"absolute bottom-4 right-4 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 4v16m8-8H4\",\n                                                                className: \"jsx-afe428e068c5954d\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, card.id, true, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex justify-center items-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        id: \"prevBtn\",\n                                        onClick: ()=>scrollToCard(\"prev\"),\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        id: \"nextBtn\",\n                                        onClick: ()=>scrollToCard(\"next\"),\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Product_product_compare__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            categorySlug: \"inspiration-cube\",\n                            locale: locale,\n                            channel: channel\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: selectedCard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4\",\n                    onClick: ()=>setSelectedCard(null),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-white relative rounded-2xl max-w-[1200px] w-full max-h-[90vh] overflow-y-auto p-[74px] my-8\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setSelectedCard(null);\n                                },\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"absolute top-4 right-4 w-10 h-10 bg-[#333336] backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-opacity-80 transition-colors z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-[#d6d6d7]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\",\n                                        className: \"jsx-afe428e068c5954d\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"relative overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[18px] font-medium text-[#1d1d1f] mb-8\",\n                                            children: selectedCard.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"text-3xl md:text-[50px] font-semibold mb-[64px] text-[#1d1d1f]\",\n                                            children: \"一直飙一直快\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[64px] bg-[#f5f5f7] h-[700px] mb-[50px] rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[64px] bg-[#f5f5f7] h-[700px] rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"afe428e068c5954d\",\n                children: \".cube-products-swiper .swiper-pagination{bottom:0!important}.cube-products-swiper .swiper-pagination-bullet{width:8px!important;height:8px!important;margin:0 4px!important;opacity:.5!important}.cube-products-swiper .swiper-pagination-bullet-active{opacity:1!important}@media(max-width:768px){#cardContainer{-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;-webkit-overflow-scrolling:touch}#cardContainer>div>div{scroll-snap-align:start}}.cube-products-swiper .swiper-slide{height:auto!important}}\\n        .scrollbar-hide {-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide::-webkit-scrollbar{display:none}.overflow-x-auto{scroll-behavior:smooth}@media(max-width:768px){.cube-products-swiper .swiper-pagination{bottom:-8px!important}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InspirationCubeLayout, \"mj8vv/8ODgZaZiUdzoqSil2Ey0o=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations\n    ];\n});\n_c = InspirationCubeLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (/*#__PURE__*/_c1 = react__WEBPACK_IMPORTED_MODULE_2___default().memo(InspirationCubeLayout));\nvar _c, _c1;\n$RefreshReg$(_c, \"InspirationCubeLayout\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx\n"));

/***/ })

});