"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/layout",{

/***/ "(app-pages-browser)/./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/navigation */ \"(app-pages-browser)/./src/navigation.ts\");\n/* harmony import */ var _components_Image_SEOOptimizedImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Image/SEOOptimizedImage */ \"(app-pages-browser)/./src/components/Image/SEOOptimizedImage.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! swiper/css */ \"(app-pages-browser)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/pagination */ \"(app-pages-browser)/./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! swiper/css/navigation */ \"(app-pages-browser)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_swiper_bundle_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! swiper/swiper-bundle.css */ \"(app-pages-browser)/./node_modules/swiper/swiper-bundle.css\");\n/* harmony import */ var _components_Product_product_compare__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Product/product-compare */ \"(app-pages-browser)/./src/components/Product/product-compare.tsx\");\n/* harmony import */ var _lib_api_product__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api/product */ \"(app-pages-browser)/./src/lib/api/product.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction InspirationCubeLayout(param) {\n    let { children, channel, locale, categoriesData, products } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations)();\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [cubeProducts, setCubeProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // 产品图片映射 - 根据产品slug映射到对应的展示图片\n    const productImageMap = {\n        \"cube-s1\": \"/image/ldc/cube S1.png\",\n        \"cube-s2\": \"/image/ldc/cube S2.png\",\n        \"cube-m1-the-ultimate-soundproof-workspace-pod\": \"/image/ldc/cube M1.png\",\n        \"cube-m2-elevate-your-workspace-with-modern-design\": \"/image/ldc/cube M2.png\",\n        \"cube-l1\": \"/image/ldc/cube L1.png\",\n        \"cube-l2\": \"/image/ldc/cube L2.png\"\n    };\n    // 获取产品展示图片\n    const getProductDisplayImage = (product)=>{\n        // 使用映射的图片\n        return productImageMap[product.slug];\n    };\n    // 获取inspiration-cube分类的产品数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchCubeProducts = async ()=>{\n            try {\n                var _response_category_products, _response_category;\n                setLoading(true);\n                const response = await (0,_lib_api_product__WEBPACK_IMPORTED_MODULE_12__.fetchProductByCategoryDataWithLimit)({\n                    slug: \"inspiration-cube\",\n                    locale: locale,\n                    channel: channel,\n                    first: 50,\n                    after: \"\"\n                });\n                if (response === null || response === void 0 ? void 0 : (_response_category = response.category) === null || _response_category === void 0 ? void 0 : (_response_category_products = _response_category.products) === null || _response_category_products === void 0 ? void 0 : _response_category_products.edges) {\n                    const productList = response.category.products.edges.map((edge)=>edge.node);\n                    setCubeProducts(productList);\n                }\n            } catch (error) {\n                console.error(\"Error fetching cube products:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCubeProducts();\n    }, [\n        locale,\n        channel\n    ]);\n    // 滚动控制函数 - 简化版本\n    const scrollToCard = (direction)=>{\n        const container = document.getElementById(\"cardContainer\");\n        if (!container) return;\n        const cardWidth = window.innerWidth >= 768 ? 384 + 24 : 320 + 16; // w-96 + gap-6 或 w-80 + gap-4\n        const scrollAmount = cardWidth; // 每次滚动一个卡片的宽度\n        if (direction === \"next\") {\n            container.scrollBy({\n                left: scrollAmount,\n                behavior: \"smooth\"\n            });\n        } else {\n            container.scrollBy({\n                left: -scrollAmount,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 更新按钮状态\n    const updateButtonStates = (container)=>{\n        const prevBtn = document.getElementById(\"prevBtn\");\n        const nextBtn = document.getElementById(\"nextBtn\");\n        if (prevBtn && nextBtn) {\n            // 禁用/启用左侧按钮\n            if (container.scrollLeft <= 10) {\n                prevBtn.setAttribute(\"disabled\", \"true\");\n            } else {\n                prevBtn.removeAttribute(\"disabled\");\n            }\n            // 禁用/启用右侧按钮\n            const maxScroll = container.scrollWidth - container.clientWidth;\n            if (container.scrollLeft >= maxScroll - 10) {\n                nextBtn.setAttribute(\"disabled\", \"true\");\n            } else {\n                nextBtn.removeAttribute(\"disabled\");\n            }\n        }\n    };\n    // 初始化按钮状态\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const container = document.getElementById(\"cardContainer\");\n        if (container) {\n            // 更新按钮状态\n            updateButtonStates(container);\n        }\n    }, []);\n    // 卡片数据 - 简化版本\n    const cardData = [\n        {\n            id: \"card-1\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-2\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-3\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-4\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-5\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-6\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-7\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        },\n        {\n            id: \"card-8\",\n            title: \"严丝合缝的舱体\",\n            subtitle: \"纵享属于你的专属空间\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"jsx-afe428e068c5954d\" + \" \" + \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"bg-[#fafafc] py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-afe428e068c5954d\" + \" \" + \"container !max-w-[850px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                // initial={{ opacity: 0, y: 30 }}\n                                // animate={{ opacity: 1, y: 0 }}\n                                // transition={{ duration: 0.8, delay: 0.4 }}\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Autoplay,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_6__.Pagination\n                                        ],\n                                        spaceBetween: 16,\n                                        slidesPerView: 2,\n                                        // autoplay={{\n                                        //   delay: 3000,\n                                        //   disableOnInteraction: false,\n                                        // }}\n                                        loop: true,\n                                        centeredSlides: false,\n                                        // pagination={{\n                                        //   clickable: true,\n                                        //   bulletClass: 'swiper-pagination-bullet !bg-gray-400',\n                                        //   bulletActiveClass: 'swiper-pagination-bullet-active !bg-gray-800',\n                                        // }}\n                                        navigation: {\n                                            nextEl: \".swiper-button-next-custom\",\n                                            prevEl: \".swiper-button-prev-custom\"\n                                        },\n                                        breakpoints: {\n                                            320: {\n                                                slidesPerView: 3,\n                                                spaceBetween: 12\n                                            },\n                                            640: {\n                                                slidesPerView: 3,\n                                                spaceBetween: 16\n                                            },\n                                            768: {\n                                                slidesPerView: 4,\n                                                spaceBetween: 20\n                                            },\n                                            1024: {\n                                                slidesPerView: 6,\n                                                spaceBetween: 24\n                                            }\n                                        },\n                                        className: \"cube-products-swiper\",\n                                        children: loading ? // 加载状态\n                                        Array.from({\n                                            length: 6\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                className: \"h-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"flex flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-[60px] h-[60px] bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-16 h-3 bg-gray-200 rounded mt-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, \"loading-\".concat(index), false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)) : cubeProducts.map((product, index)=>{\n                                            var _product_translation;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide, {\n                                                className: \"h-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.6 + index * 0.1\n                                                    },\n                                                    className: \"group cursor-pointer h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navigation__WEBPACK_IMPORTED_MODULE_3__.Link, {\n                                                        href: \"/product/\".concat(product.slug),\n                                                        className: \"group\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"flex flex-col items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"w-[60px] h-[60px] overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Image_SEOOptimizedImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        src: getProductDisplayImage(product),\n                                                                        alt: ((_product_translation = product.translation) === null || _product_translation === void 0 ? void 0 : _product_translation.name) || product.name,\n                                                                        width: 1000,\n                                                                        height: 100,\n                                                                        className: \"w-full h-full object-contain  transition-transform duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"flex-1 flex flex-col justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[12px] text-[#323232] group-hover:text-[#000] text-center mt-2 line-clamp-1\",\n                                                                        children: [\n                                                                            \"ins.\",\n                                                                            product.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200  flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-black\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8  rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200 flex\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-black\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[80px] max-lg:py-[40px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex items-center justify-between flex-wrap gap-4 gap-y-8 container mb-[80px] max-lg:mb-[40px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[80px] max-lg:text-[48px] font-semibold\",\n                                        children: \"ins.Cube\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[28px] max-lg:text-[20px] font-semibold\",\n                                        children: \"Only sound for inspiration\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"bg-gray-500 h-[700px]\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-full bg-white py-16 md:py-24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"max-w-[1680px] mx-auto px-4 mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n                                    children: \"来了解一下 Inspiration Cube\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"relative max-2xl:px-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"cardContainer\",\n                                    onScroll: (e)=>updateButtonStates(e.target),\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"overflow-x-auto overflow-y-visible scrollbar-hide\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            paddingLeft: \"max(0px, calc((100vw - 1680px) / 2))\",\n                                            paddingRight: \"max(0px, calc((100vw - 1680px) / 2))\"\n                                        },\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"flex gap-4 md:gap-6 pb-4 py-4\",\n                                        children: cardData.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    height: \"calc((100vw - 6rem) * 1.46)\",\n                                                    maxHeight: \"591px\"\n                                                },\n                                                onClick: ()=>setSelectedCard(card),\n                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex-shrink-0 bg-black rounded-3xl relative overflow-hidden hover:scale-[1.02] cursor-pointer transition-transform duration-300 w-[calc(100vw-6rem)] md:w-[405px] \".concat(index === cardData.length - 1 ? \"mr-4 md:mr-6\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"p-6 bg-black text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"text-sm font-medium text-gray-400 mb-2\",\n                                                                children: card.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-afe428e068c5954d\" + \" \" + \"text-xl font-bold leading-tight\",\n                                                                children: card.subtitle.split(\"\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-afe428e068c5954d\",\n                                                                        children: [\n                                                                            line,\n                                                                            i < card.subtitle.split(\"\\n\").length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                                className: \"jsx-afe428e068c5954d\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 74\n                                                                            }, this)\n                                                                        ]\n                                                                    }, i, true, {\n                                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"absolute bottom-4 right-4 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-4 h-4 text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 4v16m8-8H4\",\n                                                                className: \"jsx-afe428e068c5954d\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, card.id, true, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"flex justify-center items-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        id: \"prevBtn\",\n                                        onClick: ()=>scrollToCard(\"prev\"),\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        id: \"nextBtn\",\n                                        onClick: ()=>scrollToCard(\"next\"),\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\",\n                                                className: \"jsx-afe428e068c5954d\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-afe428e068c5954d\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Product_product_compare__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            categorySlug: \"inspiration-cube\",\n                            locale: locale,\n                            channel: channel\n                        }, void 0, false, {\n                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: selectedCard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4\",\n                    onClick: ()=>setSelectedCard(null),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        className: \"bg-white relative rounded-2xl max-w-[1200px] w-full max-h-[90vh] overflow-y-auto p-[74px] my-8\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setSelectedCard(null);\n                                },\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"absolute top-4 right-4 w-10 h-10 bg-[#333336] backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-opacity-80 transition-colors z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"w-5 h-5 text-[#d6d6d7]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\",\n                                        className: \"jsx-afe428e068c5954d\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"relative overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-afe428e068c5954d\" + \" \" + \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"text-[18px] font-medium text-[#1d1d1f] mb-8\",\n                                            children: selectedCard.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"jsx-afe428e068c5954d\" + \" \" + \"text-3xl md:text-[50px] font-semibold mb-[64px] text-[#1d1d1f]\",\n                                            children: \"一直飙一直快\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-afe428e068c5954d\" + \" \" + \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[64px] bg-[#f5f5f7] h-[700px] mb-[50px] rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-afe428e068c5954d\" + \" \" + \"py-[64px] bg-[#f5f5f7] h-[700px] rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\2024-10-08\\\\saleor-web-gitlab-gpjj\\\\saleor-web\\\\src\\\\components\\\\ProductsLayoutPage\\\\InspirationCubeLayout.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"afe428e068c5954d\",\n                children: \".cube-products-swiper .swiper-pagination{bottom:0!important}.cube-products-swiper .swiper-pagination-bullet{width:8px!important;height:8px!important;margin:0 4px!important;opacity:.5!important}.cube-products-swiper .swiper-pagination-bullet-active{opacity:1!important}@media(max-width:768px){#cardContainer{-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;-webkit-overflow-scrolling:touch}#cardContainer>div>div{scroll-snap-align:start}}.cube-products-swiper .swiper-slide{height:auto!important}}\\n        .scrollbar-hide {-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide::-webkit-scrollbar{display:none}.overflow-x-auto{scroll-behavior:smooth}@media(max-width:768px){.cube-products-swiper .swiper-pagination{bottom:-8px!important}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InspirationCubeLayout, \"mj8vv/8ODgZaZiUdzoqSil2Ey0o=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations\n    ];\n});\n_c = InspirationCubeLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (/*#__PURE__*/_c1 = react__WEBPACK_IMPORTED_MODULE_2___default().memo(InspirationCubeLayout));\nvar _c, _c1;\n$RefreshReg$(_c, \"InspirationCubeLayout\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductsLayoutPage/InspirationCubeLayout.tsx\n"));

/***/ })

});